#!/bin/bash

# Istanbul Pharmacy Scraper Runner
# This script runs the pharmacy scraper for Istanbul

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to script directory
cd "$SCRIPT_DIR"

# Set up logging
LOG_FILE="$SCRIPT_DIR/scraper_run.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$DATE] Starting Istanbul pharmacy scraper..." >> "$LOG_FILE"

# Run the Python scraper
python3 pharmacy_scraper.py "$@" 2>&1 | tee -a "$LOG_FILE"

# Check exit status
if [ $? -eq 0 ]; then
    echo "[$DATE] Scraper completed successfully" >> "$LOG_FILE"
else
    echo "[$DATE] Scraper failed with error" >> "$LOG_FILE"
fi

echo "[$DATE] Scraper run finished" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"
