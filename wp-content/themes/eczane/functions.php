<?php
/**
 * Eczane Theme Functions
 *
 * @package EczaneTheme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function eczane_theme_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));

    // Set content width
    global $content_width;
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'eczane_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function eczane_enqueue_assets() {
    // Styles
    wp_enqueue_style('eczane-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap', array(), null);
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0');
    wp_enqueue_style('eczane-style', get_stylesheet_uri(), array(), wp_get_theme()->get('Version'));

    // Scripts
    wp_enqueue_script('eczane-script', get_template_directory_uri() . '/script.js', array(), wp_get_theme()->get('Version'), true);

    // Localize script for AJAX
    wp_localize_script('eczane-script', 'eczane_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('eczane_nonce'),
        'site_url' => home_url(),
    ));
}
add_action('wp_enqueue_scripts', 'eczane_enqueue_assets');

/**
 * Create Custom Database Tables
 */
function eczane_create_tables() {
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();

    // Pharmacies table
    $table_pharmacies = $wpdb->prefix . 'eczane_pharmacies';
    $sql_pharmacies = "CREATE TABLE $table_pharmacies (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL,
        address text NOT NULL,
        phone varchar(50) DEFAULT '',
        district varchar(100) NOT NULL,
        latitude decimal(10, 8) DEFAULT NULL,
        longitude decimal(11, 8) DEFAULT NULL,
        city varchar(100) DEFAULT 'İSTANBUL',
        duty_date date NOT NULL,
        scraped_at datetime NOT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY district (district),
        KEY duty_date (duty_date),
        KEY district_date (district, duty_date)
    ) $charset_collate;";

    // Districts table
    $table_districts = $wpdb->prefix . 'eczane_districts';
    $sql_districts = "CREATE TABLE $table_districts (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        name varchar(100) NOT NULL,
        slug varchar(100) NOT NULL,
        city varchar(100) DEFAULT 'İSTANBUL',
        pharmacy_count int(11) DEFAULT 0,
        last_updated datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY slug (slug),
        KEY name (name)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql_pharmacies);
    dbDelta($sql_districts);

    // Update version
    update_option('eczane_db_version', '1.0');
}

/**
 * Theme Activation
 */
function eczane_theme_activation() {
    // Create tables
    eczane_create_tables();

    // Add rewrite rules
    eczane_add_rewrite_rules();

    // Flush rewrite rules
    flush_rewrite_rules();

    // Schedule cron job for data updates
    if (!wp_next_scheduled('eczane_update_data')) {
        wp_schedule_event(strtotime('09:00:00'), 'daily', 'eczane_update_data');
    }
}
add_action('after_switch_theme', 'eczane_theme_activation');

/**
 * Add Custom Rewrite Rules
 */
function eczane_add_rewrite_rules() {
    // District pages: /ilce/kadikoy
    add_rewrite_rule(
        '^ilce/([^/]+)/?$',
        'index.php?eczane_district=$matches[1]',
        'top'
    );

    // District with date: /ilce/kadikoy/2025-05-29
    add_rewrite_rule(
        '^ilce/([^/]+)/([0-9]{4}-[0-9]{2}-[0-9]{2})/?$',
        'index.php?eczane_district=$matches[1]&eczane_date=$matches[2]',
        'top'
    );
}
add_action('init', 'eczane_add_rewrite_rules');

/**
 * Add Query Vars
 */
function eczane_query_vars($vars) {
    $vars[] = 'eczane_district';
    $vars[] = 'eczane_date';
    return $vars;
}
add_filter('query_vars', 'eczane_query_vars');

/**
 * Template Redirect
 */
function eczane_template_redirect() {
    $district = get_query_var('eczane_district');

    if ($district) {
        // Load district template
        include(get_template_directory() . '/district-template.php');
        exit;
    }
}
add_action('template_redirect', 'eczane_template_redirect');

/**
 * Update Pharmacy Data from JSON
 */
function eczane_update_data_from_json() {
    $scripts_path = ABSPATH . 'scripts/';

    // Get today's and tomorrow's dates
    $today = date('d_m_Y');
    $tomorrow = date('d_m_Y', strtotime('+1 day'));

    $files_to_process = array(
        $scripts_path . "istanbul_pharmacies_{$today}.json",
        $scripts_path . "istanbul_pharmacies_{$tomorrow}.json"
    );

    $processed_count = 0;
    foreach ($files_to_process as $file_path) {
        if (file_exists($file_path)) {
            $result = eczane_process_json_file($file_path);
            if ($result) {
                $processed_count++;
                error_log("Eczane: Successfully processed " . basename($file_path));
            } else {
                error_log("Eczane: Failed to process " . basename($file_path));
            }
        } else {
            error_log("Eczane: File not found - " . basename($file_path));
        }
    }

    // Update district counts
    eczane_update_district_counts();

    // Log the update
    error_log("Eczane: Data update completed. Processed {$processed_count} files.");

    return $processed_count;
}
add_action('eczane_update_data', 'eczane_update_data_from_json');

/**
 * Process JSON File
 */
function eczane_process_json_file($file_path) {
    global $wpdb;

    $json_content = file_get_contents($file_path);
    $data = json_decode($json_content, true);

    if (!$data || !isset($data['pharmacies'])) {
        return false;
    }

    $table_pharmacies = $wpdb->prefix . 'eczane_pharmacies';
    $duty_date = date('Y-m-d', strtotime(str_replace('/', '-', $data['date'])));

    // Delete existing data for this date
    $wpdb->delete($table_pharmacies, array('duty_date' => $duty_date));

    // Insert new data
    foreach ($data['pharmacies'] as $pharmacy) {
        $wpdb->insert(
            $table_pharmacies,
            array(
                'name' => sanitize_text_field($pharmacy['name']),
                'address' => sanitize_textarea_field($pharmacy['address']),
                'phone' => sanitize_text_field($pharmacy['phone']),
                'district' => sanitize_text_field($pharmacy['district']),
                'latitude' => !empty($pharmacy['latitude']) ? floatval($pharmacy['latitude']) : null,
                'longitude' => !empty($pharmacy['longitude']) ? floatval($pharmacy['longitude']) : null,
                'city' => sanitize_text_field($pharmacy['city']),
                'duty_date' => $duty_date,
                'scraped_at' => date('Y-m-d H:i:s', strtotime($pharmacy['scraped_at']))
            )
        );
    }

    return true;
}

/**
 * Update District Counts
 */
function eczane_update_district_counts() {
    global $wpdb;

    $table_pharmacies = $wpdb->prefix . 'eczane_pharmacies';
    $table_districts = $wpdb->prefix . 'eczane_districts';

    // Get unique districts from pharmacies
    $districts = $wpdb->get_results("
        SELECT district, COUNT(*) as count
        FROM $table_pharmacies
        WHERE duty_date >= CURDATE()
        GROUP BY district
    ");

    foreach ($districts as $district) {
        $slug = sanitize_title($district->district);

        // Insert or update district
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_districts WHERE slug = %s",
            $slug
        ));

        if ($existing) {
            $wpdb->update(
                $table_districts,
                array('pharmacy_count' => $district->count),
                array('id' => $existing)
            );
        } else {
            $wpdb->insert(
                $table_districts,
                array(
                    'name' => $district->district,
                    'slug' => $slug,
                    'pharmacy_count' => $district->count
                )
            );
        }
    }
}

/**
 * Get Pharmacies by District
 */
function eczane_get_pharmacies_by_district($district_slug, $date = null) {
    global $wpdb;

    if (!$date) {
        $date = date('Y-m-d');
    }

    $table_pharmacies = $wpdb->prefix . 'eczane_pharmacies';
    $table_districts = $wpdb->prefix . 'eczane_districts';

    // Get district name from slug
    $district_name = $wpdb->get_var($wpdb->prepare(
        "SELECT name FROM $table_districts WHERE slug = %s",
        $district_slug
    ));

    if (!$district_name) {
        return array();
    }

    // Get pharmacies
    $pharmacies = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_pharmacies
         WHERE district = %s AND duty_date = %s
         ORDER BY name ASC",
        $district_name,
        $date
    ));

    return $pharmacies;
}

/**
 * Get All Districts
 */
function eczane_get_all_districts() {
    global $wpdb;

    $table_districts = $wpdb->prefix . 'eczane_districts';

    return $wpdb->get_results(
        "SELECT * FROM $table_districts ORDER BY name ASC"
    );
}

/**
 * AJAX Handler for Search
 */
function eczane_ajax_search() {
    check_ajax_referer('eczane_nonce', 'nonce');

    $query = sanitize_text_field($_POST['query']);
    $districts = eczane_get_all_districts();

    $results = array();
    foreach ($districts as $district) {
        if (stripos($district->name, $query) !== false) {
            $results[] = array(
                'name' => $district->name,
                'slug' => $district->slug,
                'url' => home_url('/ilce/' . $district->slug),
                'count' => $district->pharmacy_count
            );
        }
    }

    wp_send_json_success($results);
}
add_action('wp_ajax_eczane_search', 'eczane_ajax_search');
add_action('wp_ajax_nopriv_eczane_search', 'eczane_ajax_search');

/**
 * Manual Data Update (for testing)
 */
function eczane_manual_update() {
    if (current_user_can('manage_options') && isset($_GET['eczane_update'])) {
        eczane_update_data_from_json();
        wp_redirect(add_query_arg('updated', '1', remove_query_arg('eczane_update')));
        exit;
    }
}
add_action('admin_init', 'eczane_manual_update');

/**
 * Add Admin Menu
 */
function eczane_admin_menu() {
    add_menu_page(
        'Eczane Yönetimi',
        'Eczane Yönetimi',
        'manage_options',
        'eczane-management',
        'eczane_admin_page',
        'dashicons-location-alt',
        30
    );
}
add_action('admin_menu', 'eczane_admin_menu');

/**
 * Admin Page
 */
function eczane_admin_page() {
    global $wpdb;

    // Handle form submissions
    if (isset($_POST['import_data']) && wp_verify_nonce($_POST['_wpnonce'], 'eczane_import')) {
        eczane_update_data_from_json();
        echo '<div class="notice notice-success"><p>Veriler başarıyla güncellendi!</p></div>';
    }

    if (isset($_POST['create_tables']) && wp_verify_nonce($_POST['_wpnonce'], 'eczane_tables')) {
        eczane_create_tables();
        echo '<div class="notice notice-success"><p>Tablolar başarıyla oluşturuldu!</p></div>';
    }

    // Get statistics
    $table_pharmacies = $wpdb->prefix . 'eczane_pharmacies';
    $table_districts = $wpdb->prefix . 'eczane_districts';

    $pharmacy_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_pharmacies");
    $district_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_districts");
    $today_count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_pharmacies WHERE duty_date = %s",
        date('Y-m-d')
    ));

    ?>
    <div class="wrap">
        <h1>Eczane Yönetimi</h1>

        <div class="card">
            <h2>İstatistikler</h2>
            <table class="widefat">
                <tr>
                    <td><strong>Toplam Eczane Kayıtları:</strong></td>
                    <td><?php echo number_format($pharmacy_count); ?></td>
                </tr>
                <tr>
                    <td><strong>Toplam İlçe:</strong></td>
                    <td><?php echo number_format($district_count); ?></td>
                </tr>
                <tr>
                    <td><strong>Bugünkü Nöbetçi Eczaneler:</strong></td>
                    <td><?php echo number_format($today_count); ?></td>
                </tr>
            </table>
        </div>

        <div class="card">
            <h2>Veri Yönetimi</h2>

            <form method="post" style="margin-bottom: 20px;">
                <?php wp_nonce_field('eczane_tables'); ?>
                <input type="submit" name="create_tables" class="button button-secondary" value="Tabloları Oluştur/Güncelle">
                <p class="description">Veritabanı tablolarını oluşturur veya günceller.</p>
            </form>

            <form method="post">
                <?php wp_nonce_field('eczane_import'); ?>
                <input type="submit" name="import_data" class="button button-primary" value="JSON Verilerini İçe Aktar">
                <p class="description">Scripts klasöründeki JSON dosyalarından verileri içe aktarır.</p>
            </form>
        </div>

        <div class="card">
            <h2>Son Güncellenen İlçeler</h2>
            <?php
            $recent_districts = $wpdb->get_results("
                SELECT * FROM $table_districts
                ORDER BY last_updated DESC
                LIMIT 10
            ");

            if ($recent_districts) {
                echo '<ul>';
                foreach ($recent_districts as $district) {
                    $url = home_url('/ilce/' . $district->slug);
                    echo '<li>';
                    echo '<a href="' . esc_url($url) . '" target="_blank">' . esc_html($district->name) . '</a>';
                    echo ' (' . $district->pharmacy_count . ' eczane)';
                    echo ' - ' . date('d.m.Y H:i', strtotime($district->last_updated));
                    echo '</li>';
                }
                echo '</ul>';
            } else {
                echo '<p>Henüz veri bulunmuyor.</p>';
            }
            ?>
        </div>

        <div class="card">
            <h2>Test URL\'leri</h2>
            <?php
            $sample_districts = $wpdb->get_results("
                SELECT * FROM $table_districts
                ORDER BY pharmacy_count DESC
                LIMIT 5
            ");

            if ($sample_districts) {
                echo '<ul>';
                foreach ($sample_districts as $district) {
                    $url = home_url('/ilce/' . $district->slug);
                    echo '<li><a href="' . esc_url($url) . '" target="_blank">' . esc_url($url) . '</a></li>';
                }
                echo '</ul>';
            }
            ?>
        </div>
    </div>
    <?php
}
